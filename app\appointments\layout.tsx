import { cookies } from "next/headers";
import { getMemberFromPhone } from "@/lib/actions/database";
import type { MinimalMember } from "@/lib/actions/database";
import NewModernLayout from "@/components/appointments/NewModernLayout";
import NewModernPhoneGate from "@/components/appointments/NewModernPhoneGate";

export default async function AppointmentsLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const cookieStore = await cookies();
  const phone = cookieStore.get("memberPhone")?.value || null;

  if (!phone) {
    return <NewModernPhoneGate />;
  }

  let memberInfo: MinimalMember | null = null;
  try {
    memberInfo = await getMemberFromPhone(phone);
  } catch (e) {
    memberInfo = null;
  }

  return <NewModernLayout memberInfo={memberInfo}>{children}</NewModernLayout>;
}
